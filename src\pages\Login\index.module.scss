.login-wrapper {
  display: grid;
  min-height: 100svh;
  position: relative;
}

@media (min-width: 1024px) {
  .login-wrapper {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (width >= 1024px) {
  .box2 {
    display: block;
  }
}


.login-main-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-bg-content {
  display: none;
  background-color: #1890ff;
  height: 100svh;

  img: {
    height: 100%;
    width: 100%;
  }
}


.login-form-wrapper {
  background: center/cover url("~@/assets/login.png");
  height: 500px;
  width: 400px;
  border: 0;
}
.login-form {
  
}
.login-logo {
  width: 200px;
  height: 60px;
  display: block;
  margin: 0 auto 20px;
}

.login-container {
  width: 440px;
  height: 360px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 50px rgb(0 0 0 / 10%);
}

.login-checkbox-label {
  color: #1890ff;
}
