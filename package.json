{"name": "react-jike", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@tanstack/react-query": "^5.83.0", "antd": "^5.26.6", "axios": "^1.11.0", "clsx": "^2.1.1", "normalize.css": "^3.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1", "typescript-plugin-css-modules": "^5.2.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "sass": "^1.89.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}